CREATE OR REPLACE FUNCTION public.tms_create_booking(ins_id_ bigint, form_data_ json)
 RETURNS json
 LANGUAGE plpgsql
AS $function$
-- Declarations
declare
    -- Bare minimums
    status boolean := false;
    message text := 'Internal_error';
    affected_rows integer;
    resp_data json;
    
    -- Variables from form_data
    org_id_ integer;
    usr_id_ uuid;
    ip_address_ text;
    user_agent_ text;
    capacity_id_ bigint;

    
    -- Booking logic variables
    manpower_ integer;
    job_duration_ integer; -- in minutes
    hub_travel_time_ integer := 0; -- in minutes
    total_duration_ integer;
    man_minutes_ integer;
    slot_duration_ integer := 60; -- default 60 minutes per slot
    
    -- Capacity variables
    available_capacity_ integer;
    total_cap_in_minutes_ integer;
    booked_cap_in_minutes_ integer;
    remaining_capacity_ integer;
    
  
    
    -- Timeline and update variables
    user_context_json json;
    booking_update_json json;
    timeline_resp json;
    
begin
    -- Initialize status and message
    status := false;
    message := 'Internal_error';
    
    -- Extract data from form_data
    org_id_ := (form_data_->>'org_id')::integer;
    usr_id_ := (form_data_->>'usr_id')::uuid;
    ip_address_ := form_data_->>'ip_address';
    user_agent_ := form_data_->>'user_agent';
  
 
    capacity_id_ := (form_data_->'booking_data'->>'capacity_id')::bigint;
    
    -- Validate required parameters
    if ins_id_ is null or capacity_id_ is null then
        return json_build_object('status', false, 'message', 'ins_id and capacity_id are required');
    end if;
    
    -- Get booking parameters from form_data
    manpower_ := COALESCE((form_data_->>'6b858839-f154-4573-9e9b-4b01ebeb44a7')::integer, 1);
    job_duration_ := COALESCE((form_data_->>'3151dffe-45c3-4c27-9008-0a01c0205374')::integer, 60); -- default 60 minutes
    hub_travel_time_ := 20;
 
    
    -- Calculate total duration and man-minutes
    total_duration_ := job_duration_ + hub_travel_time_;
    man_minutes_ := total_duration_ * manpower_;
    
    -- Get available capacity from cl_tx_capacity table
    select available_capacity, total_cap_in_minutes, booked_cap_in_minutes
      from cl_tx_capacity
     where db_id = capacity_id_
       and utilization < 1
       and usr_tmzone_day  >= '2025-08-11'
      into available_capacity_, total_cap_in_minutes_, booked_cap_in_minutes_;
    
    if available_capacity_ is null then
        return json_build_object('status', false, 'message', 'Capacity not found');
    end if;
    
    -- Calculate remaining capacity in minutes
    remaining_capacity_ := total_cap_in_minutes_ - booked_cap_in_minutes_;
     --add man_minutes_ in form_data 
    form_data_ := jsonb_set(form_data_ ::jsonb, '{man_minutes_}', to_jsonb(man_minutes_));
    
    if man_minutes_ <= remaining_capacity_ then
     --booking in the same slot logc        
    resp_data := tms_create_booking_within_slot(ins_id_, form_data_);

        
    else
     -- raise notice 'tms_create_booking_across_slots % ',form_data_;
      -- Call the across-slot booking function
    resp_data := tms_create_booking_across_slots(ins_id_, form_data_);

    end if;
    
    return json_build_object('status', status, 'message', message, 'data', resp_data);
    
END;
$function$
;
