CREATE OR REPLACE FUNCTION public.tms_create_booking_across_slots(ins_id_ bigint, form_data_ json)
 RETURNS json
 LANGUAGE plpgsql
AS $function$
DECLARE
    -- Existing declarations
    status boolean := false;
    message text := 'Internal_error';
    affected_rows integer;
    resp_data json;

    -- Variables from form_data
    org_id_ integer;
    usr_id_ uuid;
    ip_address_ text;
    user_agent_ text;
    capacity_id_ bigint;
    srvc_type_id_ int;
    booking_details_ json;
    resource_id_ text;

    -- Capacity variables
    available_capacity_ integer;
    total_cap_in_minutes_ integer;
    booked_cap_in_minutes_ integer;
    remaining_capacity_ integer;

    -- Booking table variables
    existing_booking_id_ int;
    booking_qty_ integer;

    -- Timeline and update variables
    user_context_json json;
    booking_update_json json;

    -- Loop variables
    man_minutes_left integer;
    slot_duration_ integer := 60; -- Default slot duration
    current_capacity_id bigint; -- Track capacity ID for the current slot
    booking_ids int[] := '{}';  -- Array to store booking IDs
    remaining_capacity integer;

BEGIN
    -- Extract data from form_data
    org_id_ := (form_data_->>'org_id')::integer;
    usr_id_ := (form_data_->>'usr_id')::uuid;
    ip_address_ := form_data_->>'ip_address';
    user_agent_ := form_data_->>'user_agent';
    srvc_type_id_ := form_data_->>'srvc_type_id';
    booking_details_ := form_data_->>'booking_data';
    capacity_id_ := (form_data_->'booking_data'->>'capacity_id')::bigint;

    -- Get the resource_id associated with the capacity_id
    SELECT resource_id
    INTO resource_id_
    FROM cl_tx_capacity
    WHERE db_id = capacity_id_;

    IF resource_id_ IS NULL THEN
        RETURN json_build_object('status', false, 'message', 'Resource not found');
    END IF;

    -- Calculate the total man-minutes required (passed from form_data)
    man_minutes_left := COALESCE((form_data_->>'man_minutes_')::integer, 0);
  
    -- Start loop to book across slots until man_minutes_left becomes 0
    WHILE man_minutes_left > 0 LOOP
        -- Loop over all available slots for the resource_id
        FOR current_capacity_id, available_capacity_, total_cap_in_minutes_, booked_cap_in_minutes_
            IN
            SELECT db_id, available_capacity, total_cap_in_minutes, booked_cap_in_minutes
              FROM cl_tx_capacity
             WHERE resource_id = resource_id_       
               and available_capacity > 0
               and usr_tmzone_day >= '2025-08-11' -- Ensure we only consider future slots
               and utilization < 1
             ORDER BY usr_tmzone_day ASC
            
             -- AND booked_cap_in_minutes < total_cap_in_minutes_  -- Ensure it's not full
        loop
	       
	         remaining_capacity := total_cap_in_minutes_ - booked_cap_in_minutes_;
	       
            -- Check if enough capacity is available in the current slot
            IF available_capacity_ > 0 AND booked_cap_in_minutes_ < total_cap_in_minutes_ THEN
                -- Calculate how many man-minutes to book in this slot
                booking_qty_ := LEAST(man_minutes_left, remaining_capacity);

                -- Update booking table with the current slot's booking
                INSERT INTO cl_tx_bookings (
                    capacity_id, order_id, order_label, booked_qty, c_meta, u_meta
                ) VALUES (
                    current_capacity_id, ins_id_, 'Service Request', booking_qty_,
                    row(ip_address_, user_agent_, now() at time zone 'utc'),
                    row(ip_address_, user_agent_, now() at time zone 'utc')
                ) RETURNING db_id INTO existing_booking_id_;

                -- Collect the booking ID
                booking_ids := array_append(booking_ids, existing_booking_id_::int);

                -- Reduce the remaining man-minutes to be booked
                man_minutes_left := man_minutes_left - booking_qty_;

                -- Update capacity table with the new booked amount for the current slot
                UPDATE cl_tx_capacity
                SET booked_cap_in_minutes = booked_cap_in_minutes + booking_qty_
                WHERE db_id = current_capacity_id;

                -- If no man-minutes left, exit the loop
                IF man_minutes_left <= 0 THEN
                    status := true;
                    message := 'Booking completed across slots';
                    EXIT;
                END IF;
            END IF;
        END LOOP;

        -- If no available slot was found for this iteration, break out of the loop
        IF man_minutes_left > 0 THEN
            message := 'Booking unavailable - no further available slots for the remaining man-minutes';
            EXIT;
        END IF;
    END LOOP;

    -- After booking, call tms_create_service_request to update with booking IDs
    IF status then
        resp_data := json_build_object(
                'booking_id', existing_booking_id_,
                'capacity_id', capacity_id_,
                'booking_details', booking_details_
            );
            
            -- Get user context for updating service request
            user_context_json := json_build_object(
                'org_id', org_id_,
                'usr_id', usr_id_,
                'ip_address', ip_address_,
                'user_agent', user_agent_,
                'srvc_type_id',srvc_type_id_
            );
            
           -- Convert user_context_json to jsonb before using jsonb_set
			booking_update_json := jsonb_set(user_context_json::jsonb, '{booking_id}', to_jsonb(booking_ids), true);
			booking_update_json := jsonb_set(booking_update_json ::jsonb, '{capacity_id}', to_jsonb(capacity_id_), true);
			booking_update_json := jsonb_set(booking_update_json::jsonb, '{booking_data}', to_jsonb(resp_data->'booking_details'), true);
            -- Call tms_create_service_request again to update booking_ids, booking_details, capacity_id columns
		    raise notice 'booking_update_json %',booking_update_json;
        -- Prepare the booking IDs as an array of integers
--        booking_update_json := jsonb_set(
--            '{}'::jsonb, 
--            '{booking_ids}', 
--            to_jsonb(booking_ids)
--        );

        -- Call tms_create_service_request to update the service request with the booking IDs
        PERFORM tms_create_service_request(booking_update_json, ins_id_::integer);

        -- Return the final response
        resp_data := json_build_object('status', status, 'message', message, 'booking_ids', booking_ids);
    ELSE
        -- Return failure response if booking is not successful
        resp_data := json_build_object('status', status, 'message', message);
    END IF;

    -- Return the response
    RETURN resp_data;
END;
$function$
;
