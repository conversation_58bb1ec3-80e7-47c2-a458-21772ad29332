CREATE OR REPLACE FUNCTION public.tms_create_booking_within_slot(ins_id_ bigint, form_data_ json)
 RETURNS json
 LANGUAGE plpgsql
AS $function$
declare
    -- Bare minimums
    status boolean := false;
    message text := 'Internal_error';
    affected_rows integer;
    resp_data json;
    
    -- Variables from form_data
    org_id_ integer;
    usr_id_ uuid;
    ip_address_ text;
    user_agent_ text;
    capacity_id_ bigint;
    srvc_type_id_ int;
    booking_details_ json;
    
    -- Booking logic variables
    manpower_ integer;
    job_duration_ integer; -- in minutes
    hub_travel_time_ integer := 0; -- in minutes
    total_duration_ integer;
    man_minutes_ integer;
    slot_duration_ integer := 60; -- default 60 minutes per slot
    
    -- Capacity variables
    available_capacity_ integer;
    total_cap_in_minutes_ integer;
    booked_cap_in_minutes_ integer;
    remaining_capacity_ integer;
    
    -- Booking table variables
    existing_booking_id_ int;
    existing_booking_qty_ integer;
    booking_qty_ integer;
    booking_ids int[] := '{}';
    has_existing_order_booking_ boolean := false;

    -- Timeline and update variables
    user_context_json json;
    booking_update_json json;
    timeline_resp json;

begin

    -- Initialize status and message
    status := false;
    message := 'Internal_error';
    
    -- Extract data from form_data
    org_id_ := (form_data_->>'org_id')::integer;
    usr_id_ := (form_data_->>'usr_id')::uuid;
    ip_address_ := form_data_->>'ip_address';
    user_agent_ := form_data_->>'user_agent';
    srvc_type_id_ := form_data_->>'srvc_type_id';
    booking_details_ := form_data_->>'booking_data';
    capacity_id_ := (form_data_->'booking_data'->>'capacity_id')::bigint;
    
    -- Validate required parameters
    if ins_id_ is null or capacity_id_ is null then
        return json_build_object('status', false, 'message', 'ins_id and capacity_id are required');
    end if;
    
    -- Get booking parameters from form_data
    manpower_ := COALESCE((form_data_->>'6b858839-f154-4573-9e9b-4b01ebeb44a7')::integer, 1);
    job_duration_ := COALESCE((form_data_->>'3151dffe-45c3-4c27-9008-0a01c0205374')::integer, 60); -- default 60 minutes

    hub_travel_time_ := 20;

    
    -- Calculate total duration and man-minutes
    total_duration_ := job_duration_ + hub_travel_time_;
    man_minutes_ := total_duration_ * manpower_;
    
    -- Get available capacity from cl_tx_capacity table
    select available_capacity, total_cap_in_minutes, booked_cap_in_minutes
      from cl_tx_capacity
     where db_id = capacity_id_
       and utilization < 1
       and usr_tmzone_day  >= '2025-08-11'
      into available_capacity_, total_cap_in_minutes_, booked_cap_in_minutes_;
    
    if available_capacity_ is null then
        return json_build_object('status', false, 'message', 'Capacity not found');
    end if;
    
    -- Calculate remaining capacity in minutes
    remaining_capacity_ := total_cap_in_minutes_ - booked_cap_in_minutes_;
    
    -- Check if ins_id is already present in booking table (order_id column)
    select db_id, booked_qty
      from cl_tx_bookings
     where order_id = ins_id_
       and capacity_id = capacity_id_
      into existing_booking_id_, booking_qty_;
    
    -- Booking logic: Book capacity within the slot
    if manpower_ >= available_capacity_ then
        -- Check if man-minutes (total capacity man minutes) are available
       
            booking_qty_ := man_minutes_;
            
            if existing_booking_id_ is not null then
                -- Update existing booking
                update cl_tx_bookings
                   set booked_qty = booking_qty_,
                       u_meta = row(ip_address_, user_agent_, now() at time zone 'utc')
                 where db_id = existing_booking_id_;
                
                get diagnostics affected_rows = ROW_COUNT;
                
                if affected_rows = 1 then
                    message := 'Booking updated successfully';
                    booking_ids := array_append(booking_ids, existing_booking_id_::int);
                else
                    message := 'Failed to update booking';
                    return json_build_object('status', false, 'message', message);
                end if;
            else
                -- Create new booking
                insert into cl_tx_bookings (
                    capacity_id, order_id, order_label, booked_qty, c_meta, u_meta
                ) values (
                    capacity_id_, ins_id_, 'Service Request', booking_qty_,
                    row(ip_address_, user_agent_, now() at time zone 'utc'),
                    row(ip_address_, user_agent_, now() at time zone 'utc')
                ) returning db_id into existing_booking_id_;
               
                -- Collect the booking ID
                booking_ids := array_append(booking_ids, existing_booking_id_::int);

                
                get diagnostics affected_rows = ROW_COUNT;
                
                if affected_rows = 1 then
                   update cl_tx_capacity
                      set booked_cap_in_minutes = booked_cap_in_minutes + booking_qty_,
                           u_meta = row(ip_address_, user_agent_, now() at time zone 'utc')
                    where db_id = capacity_id_;
                    message := 'New booking created successfully';
                else
                    message := 'Failed to create booking';
                    return json_build_object('status', false, 'message', message);
                end if;
            end if;
            

            
            -- Prepare booking details JSON
            resp_data := json_build_object(
                'booking_id',booking_ids ,
                'capacity_id', capacity_id_,
                'booking_details', booking_details_
            );
            
            -- Get user context for updating service request
            user_context_json := json_build_object(
                'org_id', org_id_,
                'usr_id', usr_id_,
                'ip_address', ip_address_,
                'user_agent', user_agent_,
                'srvc_type_id',srvc_type_id_
            );
            
           -- Convert user_context_json to jsonb before using jsonb_set
			booking_update_json := jsonb_set(user_context_json::jsonb, '{booking_id}', to_jsonb(booking_ids), true);
			booking_update_json := jsonb_set(booking_update_json ::jsonb, '{capacity_id}', to_jsonb(capacity_id_), true);
			booking_update_json := jsonb_set(booking_update_json::jsonb, '{booking_data}', to_jsonb(resp_data->'booking_details'), true);
            -- Call tms_create_service_request again to update booking_ids, booking_details, capacity_id columns
		    raise notice 'booking_update_json %',booking_update_json;
            PERFORM tms_create_service_request(booking_update_json, ins_id_::integer);
            
            status := true;
            
       
    else
        -- Not enough manpower available
        status := false;
        message := 'Booking unavailable - insufficient manpower';
        resp_data := json_build_object(
            'required_manpower', manpower_,
            'available_manpower', available_capacity_
        );
    end if;
    
    return json_build_object('status', status, 'message', message, 'data', resp_data);
    
END;
$function$
;
